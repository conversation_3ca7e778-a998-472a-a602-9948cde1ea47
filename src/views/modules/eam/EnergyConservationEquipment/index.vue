<template>
  <div class="network-device-monitor">
    <!-- 直接显示主页面，移除详情页面组件 -->
    <NetworkDeviceHomePage
      :event-info="state.selectedEvent"
      @jump-to="comChange"
      @event-select="eventSelectHandler"
      @back="handleBack"
      :needsPassedData="state.needsPassedData"
    />
  </div>
</template>

<script lang="ts" setup>
import { reactive } from "vue";
import NetworkDeviceHomePage from "./components/NetworkDeviceHomePage.vue";

// 数据对象
const state = reactive({
  // 选中的事件
  selectedEvent: null,
  // 需要传递的数据
  needsPassedData: {}
});

// 根据传入的值切换组件（保留接口兼容性）
const comChange = (_val: string, portableData?: Object) => {
  // 现在只有主页面，不需要切换逻辑
  state.needsPassedData = portableData || {};
};

// 处理事件选择
const eventSelectHandler = (evt: any) => {
  state.selectedEvent = evt;
};

// 处理返回事件
const handleBack = () => {
  state.needsPassedData = {};
};
</script>

<style scoped lang="scss">
.network-device-monitor {
  width: 100%;
  height: 100%;

  // 过渡动画
  .fade-transform-enter-active,
  .fade-transform-leave-active {
    transition: all 0.3s ease;
  }

  .fade-transform-enter-from {
    opacity: 0;
    transform: translateX(30px);
  }

  .fade-transform-leave-to {
    opacity: 0;
    transform: translateX(-30px);
  }
}
</style>