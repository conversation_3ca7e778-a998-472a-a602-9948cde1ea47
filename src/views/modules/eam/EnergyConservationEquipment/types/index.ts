/**
 * 网络设备监控系统类型定义
 * 精简版 - 只包含当前需要的类型
 */

// Tab类型枚举
export enum DeviceTabType {
  SERVER = 'server',
  TERMINAL = 'terminal'
}

// 设备状态枚举
export enum DeviceStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  FAULT = 'fault',
  MAINTENANCE = 'maintenance'
}

// 统计卡片数据接口
export interface StatisticCard {
  title: string;
  value: number;
  trend?: {
    type: 'up' | 'down';
    value: number;
    period: string;
  };
  chart?: {
    type: 'line' | 'bar';
    data: number[];
    color: string;
  };
}

// 趋势图表数据接口
export interface ChartDataPoint {
  date: string;
  onlineCount: number;
  totalCount: number;
}

// 趋势图表配置接口
export interface TrendChartConfig {
  title: string;
  data: ChartDataPoint[];
  filters: {
    platform: string[];
    location: string[];
    timeRange: string[];
  };
}

// 设备基础接口
interface BaseDevice {
  id: string;
  status: DeviceStatus;
  ipAddress: string;
  macAddress: string;
  assetOwner: string;
  location: string;
  operations: string[];
}

// 服务器设备接口
export interface ServerDevice extends BaseDevice {
  businessSystem: string;
  onlineDuration: string;
}

// 终端设备接口
export interface TerminalDevice extends BaseDevice {
  nearestOnlineTime: string;
  lastOnlineTime: string;
  lastOnlineDuration: string;
}

// 表格筛选条件接口
export interface TableFilters {
  searchKeyword: string;
  platform?: string;
  location?: string;
  timeRange?: string;
}

// 分页信息接口
export interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 设备列表响应接口
export interface DeviceListResponse<T> {
  list: T[];
  pagination: PaginationInfo;
}

// 主页面状态接口
export interface HomePageState {
  activeTab: DeviceTabType;
  serverDevices: ServerDevice[];
  terminalDevices: TerminalDevice[];
  tableFilters: TableFilters;
  pagination: PaginationInfo;
  loading: boolean;
}

// 组织架构接口（简化版）
export interface OrgTreeNode {
  id: string;
  name: string;
  children?: OrgTreeNode[];
}


